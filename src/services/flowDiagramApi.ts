
import { useToast } from "@/hooks/use-toast";
import { API_BASE_URL } from '@/utils/endPoints';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const accessToken = localStorage.getItem('accessToken');

  if (!accessToken) {
    throw new Error('No access token found. Please log in again.');
  }

  return {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };
};

// Helper function to get current user UUID
const getCurrentUserUuid = (): string => {
  const userData = localStorage.getItem('userData');
  if (!userData) {
    throw new Error('No user data found. Please log in again.');
  }
  
  try {
    const parsedUserData = JSON.parse(userData);
    if (!parsedUserData.uuid) {
      throw new Error('User UUID not found in user data.');
    }
    return parsedUserData.uuid;
  } catch (error) {
    throw new Error('Invalid user data format.');
  }
};

// Interface for toast utilities
interface ToastUtils {
  toast: (options: {
    title: string;
    description: string;
    variant: "default" | "destructive";
  }) => void;
}

// Flow diagram tag types
export type FlowDiagramTag =
  | 'INVENTORY_MAIN'
  | 'INVENTORY_SUB'
  | 'INVENTORY_BYPRODUCT'
  | 'SCENARIO_MAIN'
  | 'SCENARIO_SUB'
  | 'SCENARIO_BYPRODUCT';

export type PeriodType = 'single' | 'multiple';

// Flow diagram interfaces based on the API schema
export interface FlowDiagramCreate {
  user_uuid: string;
  sector_uuid: string;
  name: string; // Changed from version to name
  tag: FlowDiagramTag;
  flow_type: FlowDiagramTag; // Required field at body level - same as tag
  flow_diagram: any; // The JSON schema we've been working with
  // New fields for scenario/inventory management
  period_type?: PeriodType;
  period_length?: number;
  number_of_periods?: number;
  start_date?: string; // ISO date string
  base_scenario_id?: string; // UUID of base scenario
}

export interface FlowDiagramResponse {
  uuid: string;
  user_uuid: string;
  sector_uuid: string;
  name: string; // Changed from version to name
  tag: FlowDiagramTag;
  flow_type: FlowDiagramTag; // Required field at body level - same as tag
  flow_diagram: any; // JSON object from API
  created_at: string;
  updated_at: string;
  // New fields for scenario/inventory management
  period_type?: PeriodType;
  period_length?: number;
  number_of_periods?: number;
  start_date?: string; // ISO date string
  base_scenario_id?: string; // UUID of base scenario
}

export interface FlowDiagramListResponse {
  items: FlowDiagramResponse[];
  total: number;
  page: number;
  size: number;
}

/**
 * Create a new flow diagram
 */
export const createFlowDiagram = async (
  flowDiagramData: {
    sector_uuid: string;
    name: string; // Changed from version to name
    tag: FlowDiagramTag;
    flow_type: FlowDiagramTag; // Required field - same as tag
    flow_diagram: any;
    period_type?: PeriodType;
    period_length?: number;
    number_of_periods?: number;
    start_date?: string;
    base_scenario_id?: string;
  },
  toastUtils?: ToastUtils
): Promise<FlowDiagramResponse | null> => {
  try {
    const userUuid = getCurrentUserUuid();

    // Create payload with actual flow diagram data (not dummy values)
    const createData: FlowDiagramCreate = {
      user_uuid: userUuid,
      sector_uuid: flowDiagramData.sector_uuid,
      name: flowDiagramData.name, // User's input (e.g., "My Steel Process v1.0")
      tag: flowDiagramData.tag, // New tag types
      flow_type: flowDiagramData.flow_type, // Required field at body level
      flow_diagram: flowDiagramData.flow_diagram, // Use actual flow diagram data
      period_type: flowDiagramData.period_type,
      period_length: flowDiagramData.period_length,
      number_of_periods: flowDiagramData.number_of_periods,
      start_date: flowDiagramData.start_date,
      base_scenario_id: flowDiagramData.base_scenario_id
    };

    console.log('=== FLOW DIAGRAM CREATE REQUEST ===');
    console.log('Full payload:', createData);
    console.log('Payload size:', JSON.stringify(createData).length, 'characters');
    console.log('Flow diagram object keys:', Object.keys(createData.flow_diagram || {}));
    console.log('Flow diagram nodes count:', createData.flow_diagram?.nodes?.length || 0);
    console.log('Tag:', createData.tag);
    console.log('Flow type:', createData.flow_type);
    console.log('Period type:', createData.period_type);
    console.log('Base scenario ID:', createData.base_scenario_id);
    console.log('====================================');

    const response = await fetch(`${API_BASE_URL}/flow-diagrams`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(createData)
    });

    console.log('API Response status:', response.status);
    console.log('API Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      let errorMessage = `Failed to create flow diagram: ${response.status} ${response.statusText}`;

      try {
        const errorData = await response.json();
        console.log('API Error response:', errorData);

        // Enhanced error logging for 422 validation errors
        if (response.status === 422 && errorData.detail) {
          console.log('=== VALIDATION ERROR DETAILS ===');
          if (Array.isArray(errorData.detail)) {
            errorData.detail.forEach((error, index) => {
              console.log(`Validation Error ${index + 1}:`, error);
              if (error.loc) console.log('  Location:', error.loc);
              if (error.msg) console.log('  Message:', error.msg);
              if (error.type) console.log('  Type:', error.type);
            });
          } else {
            console.log('Detail:', errorData.detail);
          }
          console.log('================================');

          // Create user-friendly error message
          if (Array.isArray(errorData.detail)) {
            const validationErrors = errorData.detail.map(err =>
              `${err.loc ? err.loc.join('.') : 'Field'}: ${err.msg || 'Invalid value'}`
            ).join('; ');
            errorMessage = `Validation failed: ${validationErrors}`;
          } else {
            errorMessage = `Validation failed: ${errorData.detail}`;
          }
        } else {
          errorMessage = errorData.detail || errorData.message || errorMessage;
        }
      } catch (parseError) {
        console.log('Could not parse error response as JSON');
        // Try to get text response
        try {
          const errorText = await response.text();
          console.log('API Error text:', errorText);
          if (errorText) {
            errorMessage = errorText;
          }
        } catch (textError) {
          console.log('Could not get error text either');
        }
      }

      throw new Error(errorMessage);
    }

    const newFlowDiagram: FlowDiagramResponse = await response.json();

    if (toastUtils) {
      toastUtils.toast({
        title: "Flow Diagram Saved",
        description: `Successfully saved "${newFlowDiagram.flow_diagram?.name || newFlowDiagram.name}" (${newFlowDiagram.tag})`,
        variant: "default"
      });
    }

    console.log('Flow diagram created successfully:', newFlowDiagram);
    return newFlowDiagram;
  } catch (error) {
    console.error('Error creating flow diagram:', error);

    // Check if this is a duplicate name error - if so, re-throw it so saveOrUpdateFlowDiagram can handle it
    const errorMessage = error instanceof Error ? error.message : String(error);
    const isDuplicateError = errorMessage.toLowerCase().includes('already exists') ||
                           errorMessage.toLowerCase().includes('duplicate') ||
                           errorMessage.includes('INVENTORY_MAIN') ||
                           errorMessage.includes('SCENARIO_MAIN');

    if (isDuplicateError) {
      throw error; // Re-throw so saveOrUpdateFlowDiagram can handle the duplicate case
    }

    // For non-duplicate errors, show toast and return null
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Saving Flow Diagram",
        description: error instanceof Error ? error.message : "Failed to save flow diagram",
        variant: "destructive"
      });
    }

    return null;
  }
};

/**
 * Fetch flow diagrams list
 */
export const fetchFlowDiagrams = async (
  sectorUuid?: string,
  toastUtils?: ToastUtils
): Promise<FlowDiagramResponse[]> => {
  try {
    const url = new URL(`${API_BASE_URL}/flow-diagrams`);
    if (sectorUuid) {
      url.searchParams.append('sector_uuid', sectorUuid);
    }

    console.log('fetchFlowDiagrams: Making request to:', url.toString());
    console.log('fetchFlowDiagrams: Sector UUID:', sectorUuid);

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: getAuthHeaders()
    });

    console.log('fetchFlowDiagrams: Response status:', response.status);
    console.log('fetchFlowDiagrams: Response ok:', response.ok);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('fetchFlowDiagrams: Error response:', errorText);
      throw new Error(`Failed to fetch flow diagrams: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('fetchFlowDiagrams: Raw response data:', data);

    // Handle both array response and object with items property
    let flowDiagrams: FlowDiagramResponse[];
    if (Array.isArray(data)) {
      console.log('fetchFlowDiagrams: Response is array');
      flowDiagrams = data;
    } else if (data?.items && Array.isArray(data.items)) {
      console.log('fetchFlowDiagrams: Response has items array');
      flowDiagrams = data.items;
    } else {
      console.log('fetchFlowDiagrams: Unexpected response format, returning empty array');
      flowDiagrams = [];
    }

    console.log('fetchFlowDiagrams: Final flow diagrams:', flowDiagrams);
    console.log('fetchFlowDiagrams: Count:', flowDiagrams.length);
    return flowDiagrams;
  } catch (error) {
    console.error('Error fetching flow diagrams:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Flow Diagrams",
        description: error instanceof Error ? error.message : "Failed to load flow diagrams from server",
        variant: "destructive"
      });
    }
    
    // Return empty array as fallback
    return [];
  }
};

/**
 * Update an existing flow diagram by UUID
 */
export const updateFlowDiagram = async (
  flowDiagramUuid: string,
  flowDiagramData: {
    sector_uuid: string;
    name: string; // Changed from version to name
    tag: FlowDiagramTag;
    flow_type: FlowDiagramTag; // Required field - same as tag
    flow_diagram: any;
    period_type?: PeriodType;
    period_length?: number;
    number_of_periods?: number;
    start_date?: string;
    base_scenario_id?: string;
  },
  toastUtils?: ToastUtils
): Promise<FlowDiagramResponse | null> => {
  try {
    const userUuid = getCurrentUserUuid();

    const updateData: FlowDiagramCreate = {
      user_uuid: userUuid,
      ...flowDiagramData
    };

    console.log('Updating flow diagram:', updateData);

    const response = await fetch(`${API_BASE_URL}/flow-diagrams/${flowDiagramUuid}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(updateData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Failed to update flow diagram: ${response.status} ${response.statusText}`);
    }

    const updatedFlowDiagram: FlowDiagramResponse = await response.json();

    if (toastUtils) {
      toastUtils.toast({
        title: "Flow Diagram Updated",
        description: `Successfully updated "${updatedFlowDiagram.tag}"`,
        variant: "default"
      });
    }

    console.log('Flow diagram updated successfully:', updatedFlowDiagram);
    return updatedFlowDiagram;
  } catch (error) {
    console.error('Error updating flow diagram:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Updating Flow Diagram",
        description: error instanceof Error ? error.message : "Failed to update flow diagram",
        variant: "destructive"
      });
    }

    return null;
  }
};

/**
 * Update an existing flow diagram by name (using the name-based PUT endpoint)
 */
export const updateFlowDiagramByName = async (
  userUuid: string,
  sectorUuid: string,
  name: string,
  flowType: FlowDiagramTag,
  flowDiagramData: {
    sector_uuid: string;
    name: string;
    tag: FlowDiagramTag;
    flow_type: FlowDiagramTag;
    flow_diagram: any;
    period_type?: PeriodType;
    period_length?: number;
    number_of_periods?: number;
    start_date?: string;
    base_scenario_id?: string;
  },
  toastUtils?: ToastUtils
): Promise<FlowDiagramResponse | null> => {
  try {
    const updateData: FlowDiagramCreate = {
      user_uuid: userUuid,
      ...flowDiagramData
    };



    const response = await fetch(`${API_BASE_URL}/flow-diagrams/${userUuid}/${sectorUuid}/${encodeURIComponent(name)}/${flowType}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(updateData)
    });

    if (!response.ok) {
      let errorMessage = `Failed to update flow diagram: ${response.status} ${response.statusText}`;

      try {
        const errorData = await response.json();
        errorMessage = errorData.detail || errorData.message || errorMessage;
      } catch (parseError) {
        // Could not parse error response as JSON
      }

      throw new Error(errorMessage);
    }

    const updatedFlowDiagram: FlowDiagramResponse = await response.json();

    if (toastUtils) {
      toastUtils.toast({
        title: "Flow Diagram Updated",
        description: `Successfully updated "${updatedFlowDiagram.flow_diagram?.name || updatedFlowDiagram.name}" (${updatedFlowDiagram.tag})`,
        variant: "default"
      });
    }

    return updatedFlowDiagram;
  } catch (error) {
    console.error('Error updating flow diagram by name:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Updating Flow Diagram",
        description: error instanceof Error ? error.message : "Failed to update flow diagram",
        variant: "destructive"
      });
    }

    return null;
  }
};

/**
 * Update flow diagram constraints
 */
export const updateFlowDiagramConstraints = async (
  userUuid: string,
  sectorUuid: string,
  name: string,
  flowType: string,
  constraintsData: {
    objective_function?: string;
    cost_constraints?: any;
    energy_constraints?: any;
    emission_constraints?: any;
  },
  toastUtils?: ToastUtils
): Promise<FlowDiagramResponse | null> => {
  try {
    console.log('=== API CALL DEBUG ===');
    console.log('Constraints data being sent:', JSON.stringify(constraintsData, null, 2));
    console.log('======================');

    const response = await fetch(`${API_BASE_URL}/flow-diagrams/${userUuid}/${sectorUuid}/${encodeURIComponent(name)}/${flowType}/constraints`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(constraintsData)
    });

    if (!response.ok) {
      let errorMessage = `Failed to update constraints: ${response.status} ${response.statusText}`;

      try {
        const errorData = await response.json();
        errorMessage = errorData.detail || errorData.message || errorMessage;
      } catch (parseError) {
        // Could not parse error response as JSON
      }

      throw new Error(errorMessage);
    }

    const result: FlowDiagramResponse = await response.json();

    if (toastUtils) {
      toastUtils.toast({
        title: "Constraints Updated",
        description: "Flow diagram constraints have been successfully updated.",
        variant: "default"
      });
    }

    return result;
  } catch (error) {
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Updating Constraints",
        description: error instanceof Error ? error.message : "Failed to update constraints",
        variant: "destructive"
      });
    }

    throw error;
  }
};

/**
 * Save or update flow diagram with duplicate name handling
 */
export const saveOrUpdateFlowDiagram = async (
  flowDiagramData: {
    sector_uuid: string;
    name: string;
    tag: FlowDiagramTag;
    flow_type: FlowDiagramTag;
    flow_diagram: any;
    period_type?: PeriodType;
    period_length?: number;
    number_of_periods?: number;
    start_date?: string;
    base_scenario_id?: string;
  },
  toastUtils?: ToastUtils,
  onConfirmOverwrite?: () => Promise<boolean>
): Promise<FlowDiagramResponse | null> => {
  try {
    // First, try to create a new flow diagram
    return await createFlowDiagram(flowDiagramData, toastUtils);
  } catch (error) {
    // Check if the error is related to duplicate names
    const errorMessage = error instanceof Error ? error.message : String(error);
    const isDuplicateError = errorMessage.toLowerCase().includes('already exists') ||
                           errorMessage.toLowerCase().includes('duplicate') ||
                           errorMessage.toLowerCase().includes('unique constraint') ||
                           errorMessage.includes('409') || // Conflict status code
                           (errorMessage.includes('flow_type') && errorMessage.includes('already exists')) ||
                           errorMessage.includes('INVENTORY_MAIN') ||
                           errorMessage.includes('SCENARIO_MAIN');

    if (isDuplicateError && onConfirmOverwrite) {
      // Ask user for confirmation to overwrite
      const shouldOverwrite = await onConfirmOverwrite();

      if (shouldOverwrite) {
        // Try to update using the name-based PUT endpoint
        const userUuid = getCurrentUserUuid();
        return await updateFlowDiagramByName(
          userUuid,
          flowDiagramData.sector_uuid,
          flowDiagramData.name,
          flowDiagramData.flow_type,
          flowDiagramData,
          toastUtils
        );
      } else {
        if (toastUtils) {
          toastUtils.toast({
            title: "Save Cancelled",
            description: "Flow diagram was not saved.",
            variant: "default"
          });
        }
        return null;
      }
    } else {
      // Re-throw the original error if it's not a duplicate name issue
      throw error;
    }
  }
};

/**
 * Delete a flow diagram
 */
export const deleteFlowDiagram = async (
  flowDiagramUuid: string,
  toastUtils?: ToastUtils
): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/flow-diagrams/${flowDiagramUuid}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to delete flow diagram: ${response.status} ${response.statusText}`);
    }

    if (toastUtils) {
      toastUtils.toast({
        title: "Flow Diagram Deleted",
        description: "Flow diagram has been successfully deleted",
        variant: "default"
      });
    }

    console.log('Flow diagram deleted successfully');
    return true;
  } catch (error) {
    console.error('Error deleting flow diagram:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Deleting Flow Diagram",
        description: error instanceof Error ? error.message : "Failed to delete flow diagram",
        variant: "destructive"
      });
    }

    return false;
  }
};
