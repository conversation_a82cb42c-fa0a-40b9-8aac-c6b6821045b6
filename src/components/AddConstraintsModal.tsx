import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import {
  emissions
} from '@/components/ConnectionForm/types';
import { updateFlowDiagramConstraints } from '@/services/flowDiagramApi';
import { getSectorUuidFromIndustryId } from '@/services/activitiesApi';
import { useToastContext } from '@/contexts/ToastContext';
import { useParams } from 'react-router-dom';

interface ConstraintsData {
  objectiveFunction: {
    selectedObjective: 'total-cost' | 'energy' | 'emissions' | null;
  };
  costConstraints: {
    totalCost: { value: string; currency: string };
    capitalCost: { value: string; currency: string };
    omCost: { value: string; currency: string };
    energyCost: { value: string; currency: string };
  };
  energyConstraints: {
    totalEnergyInput: { value: string; unit: string };
    selectedEnergies: string[];
    energyLimits: { [key: string]: { upperLimit: { value: string; unit: string }; lowerLimit: { value: string; unit: string } } };
  };
  emissionConstraints: {
    totalEmissions: { value: string; unit: string };
    selectedEnergies: string[];
    energyEmissionLimits: { [key: string]: { upperLimit: { value: string; unit: string } } };
  };
}

interface AddConstraintsModalProps {
  open: boolean;
  onClose: () => void;
  onSave?: (constraints: ConstraintsData) => void;
  scenarioId: string;
  availableEnergyTypes?: string[];
  // New props for API call
  userUuid?: string;
  sectorUuid?: string;
  scenarioName?: string;
  flowType?: string;
  industryId?: string;
}

const defaultConstraints: ConstraintsData = {
  objectiveFunction: {
    selectedObjective: null,
  },
  costConstraints: {
    totalCost: { value: '', currency: 'USD' },
    capitalCost: { value: '', currency: 'USD' },
    omCost: { value: '', currency: 'USD' },
    energyCost: { value: '', currency: 'USD' },
  },
  energyConstraints: {
    totalEnergyInput: { value: '', unit: 'KJ' },
    selectedEnergies: [],
    energyLimits: {},
  },
  emissionConstraints: {
    totalEmissions: { value: '', unit: 'kg CO₂e' },
    selectedEnergies: [],
    energyEmissionLimits: {},
  },
};

const currencyOptions = ['USD', 'EUR', 'INR', 'GBP', 'JPY'];
const energyUnitOptions = ['KJ', 'J', 'GW'];
const emissionUnitOptions = ['kg CO₂e', 't CO₂e', 'g CO₂e'];

// Helper function to merge saved constraints with default structure
const mergeWithDefaults = (saved: any, defaults: ConstraintsData): ConstraintsData => {
  return {
    objectiveFunction: {
      ...defaults.objectiveFunction,
      ...saved?.objectiveFunction,
    },
    costConstraints: {
      ...defaults.costConstraints,
      ...saved?.costConstraints,
    },
    energyConstraints: {
      ...defaults.energyConstraints,
      ...saved?.energyConstraints,
      selectedEnergies: saved?.energyConstraints?.selectedEnergies || [],
      energyLimits: saved?.energyConstraints?.energyLimits || {},
    },
    emissionConstraints: {
      ...defaults.emissionConstraints,
      ...saved?.emissionConstraints,
      selectedEnergies: saved?.emissionConstraints?.selectedEnergies || [],
      energyEmissionLimits: saved?.emissionConstraints?.energyEmissionLimits || {},
    },
  };
};

export const AddConstraintsModal: React.FC<AddConstraintsModalProps> = ({
  open,
  onClose,
  onSave,
  scenarioId,
  availableEnergyTypes = ['Electricity', 'Natural Gas', 'Steam', 'Heat', 'Coal', 'Biomass'],
  userUuid,
  sectorUuid,
  scenarioName,
  flowType,
  industryId
}) => {
  const [constraints, setConstraints] = useState<ConstraintsData>(defaultConstraints);
  const [isSaving, setIsSaving] = useState(false);
  const [energySelectValue, setEnergySelectValue] = useState<string>('');
  const [emissionSelectValue, setEmissionSelectValue] = useState<string>('');
  const { toast } = useToastContext();
  const { industryId: paramIndustryId } = useParams<{ industryId: string }>();

  // Load constraints from localStorage on mount
  useEffect(() => {
    const storageKey = `constraints_${scenarioId}`;
    const saved = localStorage.getItem(storageKey);
    if (saved) {
      try {
        const parsedConstraints = JSON.parse(saved);
        setConstraints(mergeWithDefaults(parsedConstraints, defaultConstraints));
      } catch (error) {
        console.error('Error loading saved constraints:', error);
        setConstraints(defaultConstraints);
      }
    }
  }, [scenarioId]);

  // Save constraints to localStorage whenever they change
  useEffect(() => {
    const storageKey = `constraints_${scenarioId}`;
    localStorage.setItem(storageKey, JSON.stringify(constraints));
  }, [constraints, scenarioId]);

  const handleObjectiveRadioChange = (value: 'total-cost' | 'energy' | 'emissions') => {
    setConstraints(prev => ({
      ...prev,
      objectiveFunction: {
        ...prev.objectiveFunction,
        selectedObjective: value
      }
    }));
  };

  const handleCostConstraintChange = (field: keyof ConstraintsData['costConstraints'], type: 'value' | 'currency', value: string) => {
    if (type === 'value') {
      // Only allow positive numbers for value
      if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
        setConstraints(prev => ({
          ...prev,
          costConstraints: {
            ...prev.costConstraints,
            [field]: {
              ...prev.costConstraints[field],
              value
            }
          }
        }));
      }
    } else {
      setConstraints(prev => ({
        ...prev,
        costConstraints: {
          ...prev.costConstraints,
          [field]: {
            ...prev.costConstraints[field],
            currency: value
          }
        }
      }));
    }
  };

  const handleEnergyTotalChange = (type: 'value' | 'unit', value: string) => {
    if (type === 'value') {
      // Only allow positive numbers for value
      if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
        setConstraints(prev => ({
          ...prev,
          energyConstraints: {
            ...prev.energyConstraints,
            totalEnergyInput: {
              ...prev.energyConstraints.totalEnergyInput,
              value
            }
          }
        }));
      }
    } else {
      setConstraints(prev => ({
        ...prev,
        energyConstraints: {
          ...prev.energyConstraints,
          totalEnergyInput: {
            ...prev.energyConstraints.totalEnergyInput,
            unit: value
          }
        }
      }));
    }
  };

  const handleEnergySelection = (energyType: string) => {
    if (!energyType || energyType === 'select-energy') return;

    setConstraints(prev => {
      const isAlreadySelected = prev.energyConstraints.selectedEnergies.includes(energyType);
      if (isAlreadySelected) return prev;

      const newSelectedEnergies = [...prev.energyConstraints.selectedEnergies, energyType];
      const newEnergyLimits = {
        ...prev.energyConstraints.energyLimits,
        [energyType]: {
          upperLimit: { value: '', unit: 'KJ' },
          lowerLimit: { value: '', unit: 'KJ' }
        }
      };

      return {
        ...prev,
        energyConstraints: {
          ...prev.energyConstraints,
          selectedEnergies: newSelectedEnergies,
          energyLimits: newEnergyLimits
        }
      };
    });
  };

  const handleRemoveEnergy = (energyType: string) => {
    setConstraints(prev => {
      const newSelectedEnergies = prev.energyConstraints.selectedEnergies.filter(e => e !== energyType);
      const newEnergyLimits = { ...prev.energyConstraints.energyLimits };
      delete newEnergyLimits[energyType];

      return {
        ...prev,
        energyConstraints: {
          ...prev.energyConstraints,
          selectedEnergies: newSelectedEnergies,
          energyLimits: newEnergyLimits
        }
      };
    });
  };

  const handleEnergyLimitChange = (energyType: string, limitType: 'upperLimit' | 'lowerLimit', type: 'value' | 'unit', value: string) => {
    if (type === 'value') {
      // Only allow positive numbers for value
      if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
        setConstraints(prev => ({
          ...prev,
          energyConstraints: {
            ...prev.energyConstraints,
            energyLimits: {
              ...prev.energyConstraints.energyLimits,
              [energyType]: {
                ...prev.energyConstraints.energyLimits[energyType],
                [limitType]: {
                  ...prev.energyConstraints.energyLimits[energyType][limitType],
                  value
                }
              }
            }
          }
        }));
      }
    } else {
      setConstraints(prev => ({
        ...prev,
        energyConstraints: {
          ...prev.energyConstraints,
          energyLimits: {
            ...prev.energyConstraints.energyLimits,
            [energyType]: {
              ...prev.energyConstraints.energyLimits[energyType],
              [limitType]: {
                ...prev.energyConstraints.energyLimits[energyType][limitType],
                unit: value
              }
            }
          }
        }
      }));
    }
  };

  const handleEmissionTotalChange = (type: 'value' | 'unit', value: string) => {
    if (type === 'value') {
      // Only allow positive numbers for value
      if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
        setConstraints(prev => ({
          ...prev,
          emissionConstraints: {
            ...prev.emissionConstraints,
            totalEmissions: {
              ...prev.emissionConstraints.totalEmissions,
              value
            }
          }
        }));
      }
    } else {
      setConstraints(prev => ({
        ...prev,
        emissionConstraints: {
          ...prev.emissionConstraints,
          totalEmissions: {
            ...prev.emissionConstraints.totalEmissions,
            unit: value
          }
        }
      }));
    }
  };

  const handleEmissionEnergySelection = (energyType: string) => {
    if (!energyType || energyType === 'select-energy') return;

    setConstraints(prev => {
      const isAlreadySelected = prev.emissionConstraints.selectedEnergies.includes(energyType);
      if (isAlreadySelected) return prev;

      const newSelectedEnergies = [...prev.emissionConstraints.selectedEnergies, energyType];
      const newEnergyEmissionLimits = {
        ...prev.emissionConstraints.energyEmissionLimits,
        [energyType]: {
          upperLimit: { value: '', unit: 'kg CO₂e' }
        }
      };

      return {
        ...prev,
        emissionConstraints: {
          ...prev.emissionConstraints,
          selectedEnergies: newSelectedEnergies,
          energyEmissionLimits: newEnergyEmissionLimits
        }
      };
    });
  };

  const handleRemoveEmissionEnergy = (energyType: string) => {
    setConstraints(prev => {
      const newSelectedEnergies = prev.emissionConstraints.selectedEnergies.filter(e => e !== energyType);
      const newEnergyEmissionLimits = { ...prev.emissionConstraints.energyEmissionLimits };
      delete newEnergyEmissionLimits[energyType];

      return {
        ...prev,
        emissionConstraints: {
          ...prev.emissionConstraints,
          selectedEnergies: newSelectedEnergies,
          energyEmissionLimits: newEnergyEmissionLimits
        }
      };
    });
  };

  const handleEmissionEnergyLimitChange = (energyType: string, type: 'value' | 'unit', value: string) => {
    if (type === 'value') {
      // Only allow positive numbers for value
      if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
        setConstraints(prev => ({
          ...prev,
          emissionConstraints: {
            ...prev.emissionConstraints,
            energyEmissionLimits: {
              ...prev.emissionConstraints.energyEmissionLimits,
              [energyType]: {
                ...prev.emissionConstraints.energyEmissionLimits[energyType],
                upperLimit: {
                  ...prev.emissionConstraints.energyEmissionLimits[energyType].upperLimit,
                  value
                }
              }
            }
          }
        }));
      }
    } else {
      setConstraints(prev => ({
        ...prev,
        emissionConstraints: {
          ...prev.emissionConstraints,
          energyEmissionLimits: {
            ...prev.emissionConstraints.energyEmissionLimits,
            [energyType]: {
              ...prev.emissionConstraints.energyEmissionLimits[energyType],
              upperLimit: {
                ...prev.emissionConstraints.energyEmissionLimits[energyType].upperLimit,
                unit: value
              }
            }
          }
        }
      }));
    }
  };

  // Helper function to get current user UUID
  const getCurrentUserUuid = (): string => {
    const userData = localStorage.getItem('userData');
    if (!userData) {
      throw new Error('No user data found. Please log in again.');
    }

    try {
      const parsedUserData = JSON.parse(userData);
      if (!parsedUserData.uuid) {
        throw new Error('User UUID not found in user data.');
      }
      return parsedUserData.uuid;
    } catch (error) {
      throw new Error('Invalid user data format.');
    }
  };

  // Function to transform constraints data to API format
  const transformConstraintsToApiFormat = (constraints: ConstraintsData) => {
    // Ensure we have a valid objective function value
    const selectedObjective = constraints.objectiveFunction.selectedObjective;

    // Validate that the objective is one of the allowed values (if not null)
    const validObjectives = ['total-cost', 'energy', 'emissions'];
    if (selectedObjective && !validObjectives.includes(selectedObjective)) {
      throw new Error(`Invalid objective function: ${selectedObjective}. Must be one of: ${validObjectives.join(', ')}`);
    }

    // Create the structure consistent with frontend grouping and your specification
    const apiPayload = {
      objective_function: selectedObjective,
      cost_constraints: {
        total_cost_upper_limit: {
          value: constraints.costConstraints.totalCost.value ? parseFloat(constraints.costConstraints.totalCost.value) : null,
          currency: constraints.costConstraints.totalCost.currency,
        },
        capital_cost_upper_limit: {
          value: constraints.costConstraints.capitalCost.value ? parseFloat(constraints.costConstraints.capitalCost.value) : null,
          currency: constraints.costConstraints.capitalCost.currency,
        },
        om_cost_upper_limit: {
          value: constraints.costConstraints.omCost.value ? parseFloat(constraints.costConstraints.omCost.value) : null,
          currency: constraints.costConstraints.omCost.currency,
        },
        energy_cost_upper_limit: {
          value: constraints.costConstraints.energyCost.value ? parseFloat(constraints.costConstraints.energyCost.value) : null,
          currency: constraints.costConstraints.energyCost.currency,
        },
      },
      energy_constraints: {
        total_energy_input_upper_limit: {
          value: constraints.energyConstraints.totalEnergyInput.value ? parseFloat(constraints.energyConstraints.totalEnergyInput.value) : null,
          unit: constraints.energyConstraints.totalEnergyInput.unit,
        },
        energy: Object.fromEntries(
          Object.entries(constraints.energyConstraints.energyLimits).map(([key, value]) => [
            key,
            {
              upper_limit: {
                value: value.upperLimit.value ? parseFloat(value.upperLimit.value) : null,
                unit: value.upperLimit.unit,
              },
              lower_limit: {
                value: value.lowerLimit.value ? parseFloat(value.lowerLimit.value) : null,
                unit: value.lowerLimit.unit,
              },
            },
          ])
        ),
      },
      emission_constraints: {
        total_emissions_upper_limit: {
          value: constraints.emissionConstraints.totalEmissions.value ? parseFloat(constraints.emissionConstraints.totalEmissions.value) : null,
          unit: constraints.emissionConstraints.totalEmissions.unit,
        },
        emission: Object.fromEntries(
          Object.entries(constraints.emissionConstraints.energyEmissionLimits).map(([key, value]) => [
            key,
            {
              upper_limit: {
                value: value.upperLimit.value ? parseFloat(value.upperLimit.value) : null,
                unit: value.upperLimit.unit,
              },
            },
          ])
        ),
      },
    };

    return apiPayload;
  };

  const handleSave = async () => {
    if (isSaving) return;

    setIsSaving(true);

    try {
      // Get required parameters
      const currentUserUuid = userUuid || getCurrentUserUuid();
      const currentIndustryId = industryId || paramIndustryId;
      const currentSectorUuid = sectorUuid || (currentIndustryId ? await getSectorUuidFromIndustryId(currentIndustryId) : null);
      const currentScenarioName = scenarioName || scenarioId;
      const currentFlowType = flowType || 'SCENARIO_MAIN';

      if (!currentUserUuid || !currentSectorUuid || !currentScenarioName) {
        throw new Error('Missing required parameters for API call');
      }

      // Transform constraints to API format
      const apiConstraints = transformConstraintsToApiFormat(constraints);

      // Debug: Log the transformed payload
      console.log('=== TRANSFORMED API PAYLOAD ===');
      console.log(JSON.stringify(apiConstraints, null, 2));
      console.log('===============================');

      // Call the API
      await updateFlowDiagramConstraints(
        currentUserUuid,
        currentSectorUuid,
        currentScenarioName,
        currentFlowType,
        apiConstraints,
        { toast }
      );

      // Call the onSave callback if provided
      if (onSave) {
        onSave(constraints);
      }

      onClose();
    } catch (error) {
      toast({
        title: "Error Saving Constraints",
        description: error instanceof Error ? error.message : "Failed to save constraints",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Safe access to selectedEnergies arrays with fallbacks
  const availableEnergyOptions = availableEnergyTypes.filter(
    energy => !(constraints.energyConstraints?.selectedEnergies || []).includes(energy)
  );



  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-2xl font-bold">Add Constraints</DialogTitle>
          <DialogDescription className="text-base">
            Define optimization objectives and system constraints for your scenario.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-8 py-6">
          {/* Objective Function */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Objective Function</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <RadioGroup
                value={constraints.objectiveFunction.selectedObjective || ''}
                onValueChange={handleObjectiveRadioChange}
                className="space-y-4"
              >
                <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-gray-50">
                  <RadioGroupItem value="total-cost" id="total-cost" />
                  <Label htmlFor="total-cost" className="text-base cursor-pointer">Minimise Total Cost</Label>
                </div>
                <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-gray-50">
                  <RadioGroupItem value="energy" id="energy" />
                  <Label htmlFor="energy" className="text-base cursor-pointer">Minimise Energy Use</Label>
                </div>
                <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-gray-50">
                  <RadioGroupItem value="emissions" id="emissions" />
                  <Label htmlFor="emissions" className="text-base cursor-pointer">Minimise Emissions</Label>
                </div>
              </RadioGroup>
            </CardContent>
          </Card>

          {/* Cost or Budget Constraints */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Cost or Budget Constraints</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="totalCost" className="text-base font-medium">Total Cost</Label>
                  <div className="flex gap-2">
                    <Input
                      id="totalCost"
                      type="text"
                      placeholder="Upper Limit"
                      value={constraints.costConstraints.totalCost.value}
                      onChange={(e) => handleCostConstraintChange('totalCost', 'value', e.target.value)}
                      className="h-11 flex-1"
                    />
                    <Select value={constraints.costConstraints.totalCost.currency} onValueChange={(value) => handleCostConstraintChange('totalCost', 'currency', value)}>
                      <SelectTrigger className="h-11 w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencyOptions.map((currency) => (
                          <SelectItem key={currency} value={currency}>{currency}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="capitalCost" className="text-base font-medium">Capital Cost</Label>
                  <div className="flex gap-2">
                    <Input
                      id="capitalCost"
                      type="text"
                      placeholder="Upper Limit"
                      value={constraints.costConstraints.capitalCost.value}
                      onChange={(e) => handleCostConstraintChange('capitalCost', 'value', e.target.value)}
                      className="h-11 flex-1"
                    />
                    <Select value={constraints.costConstraints.capitalCost.currency} onValueChange={(value) => handleCostConstraintChange('capitalCost', 'currency', value)}>
                      <SelectTrigger className="h-11 w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencyOptions.map((currency) => (
                          <SelectItem key={currency} value={currency}>{currency}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="omCost" className="text-base font-medium">O&M Cost</Label>
                  <div className="flex gap-2">
                    <Input
                      id="omCost"
                      type="text"
                      placeholder="Upper Limit"
                      value={constraints.costConstraints.omCost.value}
                      onChange={(e) => handleCostConstraintChange('omCost', 'value', e.target.value)}
                      className="h-11 flex-1"
                    />
                    <Select value={constraints.costConstraints.omCost.currency} onValueChange={(value) => handleCostConstraintChange('omCost', 'currency', value)}>
                      <SelectTrigger className="h-11 w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencyOptions.map((currency) => (
                          <SelectItem key={currency} value={currency}>{currency}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="energyCost" className="text-base font-medium">Energy Cost</Label>
                  <div className="flex gap-2">
                    <Input
                      id="energyCost"
                      type="text"
                      placeholder="Upper Limit"
                      value={constraints.costConstraints.energyCost.value}
                      onChange={(e) => handleCostConstraintChange('energyCost', 'value', e.target.value)}
                      className="h-11 flex-1"
                    />
                    <Select value={constraints.costConstraints.energyCost.currency} onValueChange={(value) => handleCostConstraintChange('energyCost', 'currency', value)}>
                      <SelectTrigger className="h-11 w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencyOptions.map((currency) => (
                          <SelectItem key={currency} value={currency}>{currency}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Energy Constraints */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Energy Constraints</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="totalEnergyInput" className="text-base font-medium">Total Energy Input</Label>
                <div className="flex gap-2">
                  <Input
                    id="totalEnergyInput"
                    type="text"
                    placeholder="Upper Limit"
                    value={constraints.energyConstraints.totalEnergyInput.value}
                    onChange={(e) => handleEnergyTotalChange('value', e.target.value)}
                    className="h-11 flex-1"
                  />
                  <Select value={constraints.energyConstraints.totalEnergyInput.unit} onValueChange={(value) => handleEnergyTotalChange('unit', value)}>
                    <SelectTrigger className="h-11 w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {energyUnitOptions.map((unit) => (
                        <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-base font-medium">Select Energy Types</Label>
                  <Select value="" onValueChange={handleEnergySelection}>
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder="Select an energy type to add" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableEnergyOptions.map((energyType) => (
                        <SelectItem key={energyType} value={energyType}>
                          {energyType}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Selected Energy Types */}
                {(constraints.energyConstraints?.selectedEnergies || []).length > 0 && (
                  <div className="space-y-3">
                    <Label className="text-base font-medium">Selected Energy Types</Label>
                    <div className="flex flex-wrap gap-2">
                      {(constraints.energyConstraints?.selectedEnergies || []).map((energyType) => (
                        <Badge key={energyType} variant="secondary" className="px-3 py-1 text-sm">
                          {energyType}
                          <button
                            onClick={() => handleRemoveEnergy(energyType)}
                            className="ml-2 hover:text-red-600"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Energy Limits for Selected Types */}
                {(constraints.energyConstraints?.selectedEnergies || []).length > 0 && (
                  <div className="space-y-4">
                    <Label className="text-base font-medium">Energy Limits</Label>
                    <div className="space-y-4">
                      {(constraints.energyConstraints?.selectedEnergies || []).map((energyType) => (
                        <div key={energyType} className="p-4 border rounded-lg space-y-3">
                          <Label className="font-semibold text-base">{energyType}</Label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor={`${energyType}-upper`} className="text-sm text-gray-600">Upper Limit</Label>
                              <div className="flex gap-2">
                                <Input
                                  id={`${energyType}-upper`}
                                  type="text"
                                  placeholder="Upper Limit"
                                  value={constraints.energyConstraints.energyLimits[energyType]?.upperLimit.value || ''}
                                  onChange={(e) => handleEnergyLimitChange(energyType, 'upperLimit', 'value', e.target.value)}
                                  className="h-10 flex-1"
                                />
                                <Select 
                                  value={constraints.energyConstraints.energyLimits[energyType]?.upperLimit.unit || 'KJ'} 
                                  onValueChange={(value) => handleEnergyLimitChange(energyType, 'upperLimit', 'unit', value)}
                                >
                                  <SelectTrigger className="h-10 w-20">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {energyUnitOptions.map((unit) => (
                                      <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor={`${energyType}-lower`} className="text-sm text-gray-600">Lower Limit</Label>
                              <div className="flex gap-2">
                                <Input
                                  id={`${energyType}-lower`}
                                  type="text"
                                  placeholder="Lower Limit"
                                  value={constraints.energyConstraints.energyLimits[energyType]?.lowerLimit.value || ''}
                                  onChange={(e) => handleEnergyLimitChange(energyType, 'lowerLimit', 'value', e.target.value)}
                                  className="h-10 flex-1"
                                />
                                <Select 
                                  value={constraints.energyConstraints.energyLimits[energyType]?.lowerLimit.unit || 'KJ'} 
                                  onValueChange={(value) => handleEnergyLimitChange(energyType, 'lowerLimit', 'unit', value)}
                                >
                                  <SelectTrigger className="h-10 w-20">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {energyUnitOptions.map((unit) => (
                                      <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Emission Constraints */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Emission Constraints</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="totalEmissions" className="text-base font-medium">Total Emissions</Label>
                <div className="flex gap-2">
                  <Input
                    id="totalEmissions"
                    type="text"
                    placeholder="Upper Limit"
                    value={constraints.emissionConstraints.totalEmissions.value}
                    onChange={(e) => handleEmissionTotalChange('value', e.target.value)}
                    className="h-11 flex-1"
                  />
                  <Select value={constraints.emissionConstraints.totalEmissions.unit} onValueChange={(value) => handleEmissionTotalChange('unit', value)}>
                    <SelectTrigger className="h-11 w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {emissionUnitOptions.map((unit) => (
                        <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-base font-medium">Specific Emissions</Label>
                  <Select value="" onValueChange={handleEmissionEnergySelection}>
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder="Select an emission type for limits" />
                    </SelectTrigger>
                    <SelectContent>
                      {emissions.map((emission) => (
                        <SelectItem key={emission} value={emission}>
                          {emission}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Selected Energy Types for Emissions */}
                {(constraints.emissionConstraints?.selectedEnergies || []).length > 0 && (
                  <div className="space-y-3">
                    <Label className="text-base font-medium">Selected Energy Types</Label>
                    <div className="flex flex-wrap gap-2">
                      {(constraints.emissionConstraints?.selectedEnergies || []).map((energyType) => (
                        <Badge key={energyType} variant="secondary" className="px-3 py-1 text-sm">
                          {energyType}
                          <button
                            onClick={() => handleRemoveEmissionEnergy(energyType)}
                            className="ml-2 hover:text-red-600"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Emission Limits for Selected Energy Types */}
                {(constraints.emissionConstraints?.selectedEnergies || []).length > 0 && (
                  <div className="space-y-4">
                    <Label className="text-base font-medium">Emission Limits per Energy Type</Label>
                    <div className="space-y-4">
                      {(constraints.emissionConstraints?.selectedEnergies || []).map((energyType) => (
                        <div key={energyType} className="p-4 border rounded-lg space-y-3">
                          <Label className="font-semibold text-base">{energyType} Emissions</Label>
                          <div className="space-y-2">
                            <Label htmlFor={`${energyType}-emission-upper`} className="text-sm text-gray-600">Upper Limit</Label>
                            <div className="flex gap-2">
                              <Input
                                id={`${energyType}-emission-upper`}
                                type="text"
                                placeholder="Upper Limit"
                                value={constraints.emissionConstraints.energyEmissionLimits[energyType]?.upperLimit.value || ''}
                                onChange={(e) => handleEmissionEnergyLimitChange(energyType, 'value', e.target.value)}
                                className="h-10 flex-1"
                              />
                              <Select 
                                value={constraints.emissionConstraints.energyEmissionLimits[energyType]?.upperLimit.unit || 'kg CO₂e'} 
                                onValueChange={(value) => handleEmissionEnergyLimitChange(energyType, 'unit', value)}
                              >
                                <SelectTrigger className="h-10 w-32">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {emissionUnitOptions.map((unit) => (
                                    <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="gap-3">
          <Button variant="outline" onClick={onClose} className="h-11 px-6" disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="h-11 px-6" disabled={isSaving}>
            {isSaving ? 'Saving...' : 'Save Constraints'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
