# Constraints API Sample JSON

This document provides a sample JSON structure for the constraints API endpoint.

## API Endpoint
```
PATCH /{user_uuid}/{sector_uuid}/{name}/{flow_type}/constraints
```

## Sample JSON Request Body

```json
{
  "objective_function": {
    "selected_objective": "total-cost"
  },
  "cost_constraints": {
    "total_cost": {
      "value": 1000000,
      "currency": "USD"
    },
    "capital_cost": {
      "value": 500000,
      "currency": "USD"
    },
    "om_cost": {
      "value": 50000,
      "currency": "USD"
    },
    "energy_cost": {
      "value": 100000,
      "currency": "USD"
    }
  },
  "energy_constraints": {
    "total_energy_input": {
      "value": 10000,
      "unit": "KJ"
    },
    "selected_energies": ["Electricity", "Natural Gas"],
    "energy_limits": {
      "Electricity": {
        "upper_limit": {
          "value": 5000,
          "unit": "KJ"
        },
        "lower_limit": {
          "value": 1000,
          "unit": "KJ"
        }
      },
      "Natural Gas": {
        "upper_limit": {
          "value": 3000,
          "unit": "KJ"
        },
        "lower_limit": {
          "value": 500,
          "unit": "KJ"
        }
      }
    }
  },
  "emission_constraints": {
    "total_emissions": {
      "value": 1000,
      "unit": "kg CO₂e"
    },
    "selected_energies": ["CO2", "NOx"],
    "energy_emission_limits": {
      "CO2": {
        "upper_limit": {
          "value": 500,
          "unit": "kg CO₂e"
        }
      },
      "NOx": {
        "upper_limit": {
          "value": 100,
          "unit": "kg CO₂e"
        }
      }
    }
  }
}
```

## Field Descriptions

### objective_function
- `selected_objective`: String - One of "total-cost", "energy", "emissions", or null

### cost_constraints
All cost constraint fields have the same structure:
- `value`: Number or null - The constraint value
- `currency`: String - Currency code (USD, EUR, INR, GBP, JPY)

### energy_constraints
- `total_energy_input`: Object with value (Number or null) and unit (String)
- `selected_energies`: Array of strings - Selected energy types
- `energy_limits`: Object where keys are energy types and values contain:
  - `upper_limit`: Object with value (Number or null) and unit (String)
  - `lower_limit`: Object with value (Number or null) and unit (String)

### emission_constraints
- `total_emissions`: Object with value (Number or null) and unit (String)
- `selected_energies`: Array of strings - Selected emission types
- `energy_emission_limits`: Object where keys are emission types and values contain:
  - `upper_limit`: Object with value (Number or null) and unit (String)

## Usage Example

```tsx
import { AddConstraintsModal } from '@/components/AddConstraintsModal';

// In your component
<AddConstraintsModal
  open={showConstraintsModal}
  onClose={() => setShowConstraintsModal(false)}
  onSave={handleConstraintsAdded}
  scenarioId="my-scenario-id"
  availableEnergyTypes={['Electricity', 'Natural Gas', 'Steam', 'Heat']}
  scenarioName="My Steel Process Scenario"
  flowType="SCENARIO_MAIN"
  industryId="steel-production"
  // Optional: if you have these values directly
  userUuid="user-uuid-here"
  sectorUuid="sector-uuid-here"
/>
```

## Notes
- All numeric values can be null if not specified
- Empty strings from the form are converted to null values
- Units are preserved as specified in the form
- The API expects the flow_type to be "SCENARIO_MAIN" for scenario constraints
- If userUuid and sectorUuid are not provided, they will be automatically retrieved
- The component handles API calls automatically when "Save Constraints" is clicked
